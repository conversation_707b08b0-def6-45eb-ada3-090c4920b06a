import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class DebugService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Test notification system
  static Future<void> testNotificationSystem() async {
    debugPrint('🔧 === NOTIFICATION SYSTEM DEBUG TEST ===');

    final user = _auth.currentUser;
    if (user == null) {
      debugPrint('❌ No authenticated user found');
      return;
    }

    debugPrint('✅ Current user: ${user.uid} (${user.email})');

    try {
      // Test 1: Check if user document exists
      debugPrint('\n📋 Test 1: Checking user document...');
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data()!;
        debugPrint('✅ User document exists');
        debugPrint('📝 User data: ${userData.keys.toList()}');
        debugPrint('🔔 FCM Token: ${userData['fcmToken'] ?? 'NOT SET'}');
      } else {
        debugPrint('❌ User document does not exist');
      }

      // Test 2: Check notifications collection structure
      debugPrint('\n📋 Test 2: Checking notifications collection...');
      final notificationsRef = _firestore.collection('notifications').doc(user.uid).collection('items');
      final notificationsSnapshot = await notificationsRef.limit(5).get();

      debugPrint('📊 Total notifications: ${notificationsSnapshot.docs.length}');

      if (notificationsSnapshot.docs.isNotEmpty) {
        debugPrint('📝 Sample notification structure:');
        final sampleNotification = notificationsSnapshot.docs.first.data();
        sampleNotification.forEach((key, value) {
          debugPrint('   $key: $value (${value.runtimeType})');
        });
      } else {
        debugPrint('⚠️ No notifications found');
      }

      // Test 3: Create a test notification
      debugPrint('\n📋 Test 3: Creating test notification...');
      final testNotificationData = {
        'title': 'Debug Test Notification',
        'body': 'This is a test notification created at ${DateTime.now()}',
        'type': 'debug',
        'timestamp': FieldValue.serverTimestamp(),
        'senderId': user.uid,
        'read': false,
        'debugTest': true,
      };

      await notificationsRef.add(testNotificationData);
      debugPrint('✅ Test notification created successfully');

      // Test 4: Verify the notification was created
      debugPrint('\n📋 Test 4: Verifying test notification...');
      await Future.delayed(const Duration(seconds: 2)); // Wait for server timestamp

      final verifySnapshot = await notificationsRef.orderBy('timestamp', descending: true).limit(5).get();

      final debugNotifications = verifySnapshot.docs.where((doc) => doc.data()['debugTest'] == true).toList();

      if (debugNotifications.isNotEmpty) {
        debugPrint('✅ Test notification verified in database');
        final verifiedData = debugNotifications.first.data();
        debugPrint('📝 Verified data: $verifiedData');
      } else {
        debugPrint('❌ Test notification not found in database');
      }
    } catch (e) {
      debugPrint('❌ Error during notification test: $e');
    }

    debugPrint('\n🔧 === NOTIFICATION DEBUG TEST COMPLETE ===');
  }

  // Test sessions collection
  static Future<void> testSessionsCollection() async {
    debugPrint('🔧 === SESSIONS COLLECTION DEBUG TEST ===');

    final user = _auth.currentUser;
    if (user == null) {
      debugPrint('❌ No authenticated user found');
      return;
    }

    try {
      final sessionsSnapshot = await _firestore.collection('sessions').limit(10).get();
      debugPrint('📊 Total sessions in database: ${sessionsSnapshot.docs.length}');

      if (sessionsSnapshot.docs.isNotEmpty) {
        debugPrint('📝 Sample session structure:');
        final sampleSession = sessionsSnapshot.docs.first.data();
        sampleSession.forEach((key, value) {
          debugPrint('   $key: $value (${value.runtimeType})');
        });
      } else {
        debugPrint('⚠️ No sessions found');
      }
    } catch (e) {
      debugPrint('❌ Error testing sessions: $e');
    }

    debugPrint('🔧 === SESSIONS DEBUG TEST COMPLETE ===');
  }

  // Test messages/chats collection
  static Future<void> testMessagesCollection() async {
    debugPrint('🔧 === MESSAGES COLLECTION DEBUG TEST ===');

    final user = _auth.currentUser;
    if (user == null) {
      debugPrint('❌ No authenticated user found');
      return;
    }

    try {
      final chatsSnapshot = await _firestore.collection('chats').limit(10).get();
      debugPrint('📊 Total chats in database: ${chatsSnapshot.docs.length}');

      if (chatsSnapshot.docs.isNotEmpty) {
        debugPrint('📝 Sample chat structure:');
        final sampleChat = chatsSnapshot.docs.first.data();
        sampleChat.forEach((key, value) {
          debugPrint('   $key: $value (${value.runtimeType})');
        });
      } else {
        debugPrint('⚠️ No chats found');
      }
    } catch (e) {
      debugPrint('❌ Error testing messages: $e');
    }

    debugPrint('🔧 === MESSAGES DEBUG TEST COMPLETE ===');
  }

  // Clean up debug notifications
  static Future<void> cleanupDebugNotifications() async {
    debugPrint('🧹 Cleaning up debug notifications...');

    final user = _auth.currentUser;
    if (user == null) return;

    try {
      // Get all notifications and filter client-side to avoid index issues
      final allNotifications =
          await _firestore.collection('notifications').doc(user.uid).collection('items').limit(50).get();

      final debugNotifications = allNotifications.docs.where((doc) => doc.data()['debugTest'] == true).toList();

      final batch = _firestore.batch();
      for (final doc in debugNotifications) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      debugPrint('✅ Cleaned up ${debugNotifications.length} debug notifications');
    } catch (e) {
      debugPrint('❌ Error cleaning up debug notifications: $e');
    }
  }

  // Test stream consistency after skill posting
  static Future<void> testStreamConsistency() async {
    debugPrint('🔧 === STREAM CONSISTENCY TEST ===');

    final user = _auth.currentUser;
    if (user == null) {
      debugPrint('❌ No authenticated user found');
      return;
    }

    try {
      // Test 1: Check notifications stream
      debugPrint('\n📋 Test 1: Checking notifications stream...');
      final notificationsSnapshot =
          await _firestore.collection('notifications').doc(user.uid).collection('items').limit(3).get();
      debugPrint('✅ Notifications stream accessible: ${notificationsSnapshot.docs.length} items');

      // Test 2: Check sessions stream
      debugPrint('\n📋 Test 2: Checking sessions stream...');
      final sessionsSnapshot = await _firestore.collection('sessions').limit(3).get();
      debugPrint('✅ Sessions stream accessible: ${sessionsSnapshot.docs.length} items');

      // Test 3: Check chats stream
      debugPrint('\n📋 Test 3: Checking chats stream...');
      final chatsSnapshot = await _firestore.collection('chats').limit(3).get();
      debugPrint('✅ Chats stream accessible: ${chatsSnapshot.docs.length} items');

      // Test 4: Check skills stream
      debugPrint('\n📋 Test 4: Checking skills stream...');
      final skillsSnapshot = await _firestore.collection('skills').limit(3).get();
      debugPrint('✅ Skills stream accessible: ${skillsSnapshot.docs.length} items');
    } catch (e) {
      debugPrint('❌ Error during stream consistency test: $e');
    }

    debugPrint('🔧 === STREAM CONSISTENCY TEST COMPLETE ===');
  }

  // Test messaging without breaking other streams
  static Future<void> testMessagingIsolation() async {
    debugPrint('🔧 === MESSAGING ISOLATION TEST ===');

    final user = _auth.currentUser;
    if (user == null) {
      debugPrint('❌ No authenticated user found');
      return;
    }

    try {
      // Test 1: Check streams before sending notification
      debugPrint('\n📋 Test 1: Checking streams before notification...');
      await testStreamConsistency();

      // Test 2: Send a test notification (simulating message notification)
      debugPrint('\n📋 Test 2: Sending test message notification...');
      await _firestore.collection('notifications').doc(user.uid).collection('items').add({
        'title': 'Test Message',
        'body': 'Testing message notification isolation',
        'type': 'chat',
        'timestamp': FieldValue.serverTimestamp(),
        'senderId': user.uid,
        'read': false,
        'testMessage': true,
      });

      debugPrint('✅ Test message notification sent');

      // Test 3: Wait and check streams after notification
      debugPrint('\n📋 Test 3: Checking streams after notification...');
      await Future.delayed(const Duration(seconds: 2));
      await testStreamConsistency();
    } catch (e) {
      debugPrint('❌ Error during messaging isolation test: $e');
    }

    debugPrint('🔧 === MESSAGING ISOLATION TEST COMPLETE ===');
  }

  // Test complete functionality after fixes
  static Future<void> testCompleteFunctionality() async {
    debugPrint('🔧 === COMPLETE FUNCTIONALITY TEST ===');

    final user = _auth.currentUser;
    if (user == null) {
      debugPrint('❌ No authenticated user found');
      return;
    }

    try {
      // Test 1: Skill posting without breaking other screens
      debugPrint('\n📋 Test 1: Testing skill posting isolation...');
      await _firestore.collection('skills').add({
        'title': 'Test Skill for Isolation',
        'category': 'Testing',
        'type': 'offer',
        'userId': user.uid,
        'userEmail': user.email,
        'timestamp': FieldValue.serverTimestamp(),
        'testSkill': true,
      });
      debugPrint('✅ Test skill posted successfully');

      // Test 2: Verify all streams still work
      debugPrint('\n📋 Test 2: Verifying stream consistency after skill post...');
      await testStreamConsistency();

      // Test 3: Test chat creation
      debugPrint('\n📋 Test 3: Testing chat creation...');
      final testChatId = 'test_${user.uid}_${DateTime.now().millisecondsSinceEpoch}';
      await _firestore.collection('chats').doc(testChatId).set({
        'participants': [user.uid, 'test_user'],
        'participantNames': {user.uid: 'Test User 1', 'test_user': 'Test User 2'},
        'lastMessage': 'Test message',
        'lastMessageTime': FieldValue.serverTimestamp(),
        'createdAt': FieldValue.serverTimestamp(),
        'testChat': true,
      });
      debugPrint('✅ Test chat created successfully');

      // Test 4: Verify messaging doesn't break other screens
      debugPrint('\n📋 Test 4: Testing messaging isolation...');
      await testMessagingIsolation();
    } catch (e) {
      debugPrint('❌ Error during complete functionality test: $e');
    }

    debugPrint('🔧 === COMPLETE FUNCTIONALITY TEST COMPLETE ===');
  }

  // Run all tests
  static Future<void> runAllTests() async {
    debugPrint('🚀 === STARTING COMPREHENSIVE DEBUG TESTS ===');

    await testNotificationSystem();
    await Future.delayed(const Duration(seconds: 1));

    await testSessionsCollection();
    await Future.delayed(const Duration(seconds: 1));

    await testMessagesCollection();
    await Future.delayed(const Duration(seconds: 1));

    await testStreamConsistency();
    await Future.delayed(const Duration(seconds: 1));

    await testMessagingIsolation();
    await Future.delayed(const Duration(seconds: 1));

    await testCompleteFunctionality();

    debugPrint('🚀 === ALL DEBUG TESTS COMPLETE ===');
  }
}
