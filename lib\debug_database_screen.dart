import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class DebugDatabaseScreen extends StatefulWidget {
  const DebugDatabaseScreen({super.key});

  @override
  State<DebugDatabaseScreen> createState() => _DebugDatabaseScreenState();
}

class _DebugDatabaseScreenState extends State<DebugDatabaseScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  List<Map<String, dynamic>> _skillsData = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // Check skills collection only
      final skillsSnapshot = await _firestore.collection('skills').get();
      _skillsData =
          skillsSnapshot.docs.map((doc) {
            final data = doc.data();
            data['docId'] = doc.id;
            return data;
          }).toList();

      setState(() => _isLoading = false);

      debugPrint('🔍 Found ${_skillsData.length} skills');
    } catch (e) {
      debugPrint('❌ Error loading data: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _createTestSkill() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please log in first')));
        }
        return;
      }

      await _firestore.collection('skills').add({
        'title': 'Test Skill ${DateTime.now().millisecondsSinceEpoch}',
        'description': 'This is a test skill created from debug screen',
        'type': 'offer',
        'category': 'Programming',
        'userId': user.uid,
        'timestamp': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Test skill created!')));
      }

      _loadData(); // Refresh data
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error creating test skill: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Database'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.add), onPressed: _createTestSkill, tooltip: 'Create Test Skill'),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData, tooltip: 'Refresh'),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Current user info
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('Current User:', style: TextStyle(fontWeight: FontWeight.bold)),
                            Text('UID: ${_auth.currentUser?.uid ?? 'Not logged in'}'),
                            Text('Email: ${_auth.currentUser?.email ?? 'Not logged in'}'),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Skills collection
                    Text(
                      'Skills Collection (${_skillsData.length} items)',
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    if (_skillsData.isEmpty)
                      const Card(
                        child: Padding(padding: EdgeInsets.all(16), child: Text('No skills found in database')),
                      )
                    else
                      ..._skillsData.map(
                        (skill) => Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(skill['title'] ?? 'No title', style: const TextStyle(fontWeight: FontWeight.bold)),
                                const SizedBox(height: 4),
                                Text('Type: ${skill['type'] ?? 'Unknown'}'),
                                Text('Category: ${skill['category'] ?? 'Unknown'}'),
                                Text('User ID: ${skill['userId'] ?? 'Unknown'}'),
                                Text('Doc ID: ${skill['docId'] ?? 'Unknown'}'),
                                if (skill['description'] != null) ...[
                                  const SizedBox(height: 4),
                                  Text(skill['description'], style: const TextStyle(color: Colors.grey)),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
    );
  }
}
